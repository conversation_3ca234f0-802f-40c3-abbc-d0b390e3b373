# ESP-IDF OTA 升级 API 使用指南

## 目录

- [1. 简介](#1-简介)
- [2. 分区表配置](#2-分区表配置)
- [3. OTA 基本流程](#3-ota-基本流程)
- [4. API 详解](#4-api-详解)
  - [4.1 OTA 分区操作](#41-ota-分区操作)
  - [4.2 OTA 升级操作](#42-ota-升级操作)
  - [4.3 OTA 验证与回滚](#43-ota-验证与回滚)
- [5. 完整示例](#5-完整示例)
  - [5.1 HTTP OTA 示例](#51-http-ota-示例)
  - [5.2 HTTPS OTA 示例](#52-https-ota-示例)
- [6. 安全性考虑](#6-安全性考虑)
- [7. 常见问题与解决方案](#7-常见问题与解决方案)
- [8. 参考资料](#8-参考资料)

## 1. 简介

OTA（Over-The-Air）升级是一种允许设备通过网络接收和安装新固件的技术，无需物理连接设备。ESP-IDF 框架提供了完整的 OTA 升级支持，包括分区管理、固件下载、验证和安装等功能。

ESP-IDF 的 OTA 实现基于双分区设计，即在 Flash 中预留两个应用分区，一个用于运行当前固件，另一个用于接收新固件。这种设计确保了升级过程的安全性和可靠性。

## 2. 分区表配置

在使用 OTA 功能前，需要正确配置分区表。ESP-IDF 提供了默认的分区表配置，支持 OTA 升级。以下是一个典型的支持 OTA 的分区表示例：

```
# Name,   Type, SubType, Offset,  Size, Flags
bootloader,,    ,        0x1000,  0x7000,
partition-table,,       ,        0x8000,  0x1000,
phy_init, data, phy,    0x9000,  0x1000,
factory,  app,  factory,0x10000, 1M,
otadata,  data, ota,    ,        0x2000,
app0,     app,  ota_0,  ,        1M,
app1,     app,  ota_1,  ,        1M,
storage,  data, spiffs, ,        0x100000,
```

您可以通过修改项目中的 `partitions.csv` 文件来自定义分区表，或者在 `menuconfig` 中选择预定义的分区表：

```
idf.py menuconfig
```

然后导航到 `Partition Table` -> `Partition Table` 选择 `Factory app, two OTA definitions`。

## 3. OTA 基本流程

ESP-IDF 中的 OTA 升级流程通常包括以下步骤：

1. **版本检查**：设备向服务器查询是否有新版本固件
2. **下载固件**：从服务器下载新固件
3. **验证固件**：检查固件的完整性和兼容性
4. **写入固件**：将新固件写入备用 OTA 分区
5. **设置启动分区**：将系统引导指向新固件
6. **重启设备**：重启设备以运行新固件
7. **验证运行**：确认新固件正常运行，否则回滚

## 4. API 详解

### 4.1 OTA 分区操作

#### 获取当前运行分区

```c
const esp_partition_t* esp_ota_get_running_partition(void);
```

此函数返回当前正在运行的应用分区的信息。

#### 获取下一个 OTA 分区

```c
const esp_partition_t* esp_ota_get_next_update_partition(const esp_partition_t *start_from);
```

此函数返回下一个可用于 OTA 升级的分区。参数 `start_from` 可以设置为 NULL，表示从第一个 OTA 分区开始查找。

#### 获取启动分区

```c
const esp_partition_t* esp_ota_get_boot_partition(void);
```

此函数返回下次启动时将使用的分区。

### 4.2 OTA 升级操作

#### 开始 OTA 升级

```c
esp_err_t esp_ota_begin(const esp_partition_t *partition, size_t image_size, esp_ota_handle_t *out_handle);
```

此函数开始 OTA 升级过程，为写入新固件做准备。

- `partition`：目标 OTA 分区
- `image_size`：新固件的大小（如果未知，可以设置为 0）
- `out_handle`：输出参数，用于后续的 OTA 操作

#### 写入固件数据

```c
esp_err_t esp_ota_write(esp_ota_handle_t handle, const void *data, size_t size);
```

此函数将固件数据写入 OTA 分区。

- `handle`：由 `esp_ota_begin` 返回的句柄
- `data`：要写入的数据
- `size`：数据大小

#### 完成 OTA 升级

```c
esp_err_t esp_ota_end(esp_ota_handle_t handle);
```

此函数完成 OTA 升级过程，验证写入的固件。

- `handle`：由 `esp_ota_begin` 返回的句柄

#### 设置启动分区

```c
esp_err_t esp_ota_set_boot_partition(const esp_partition_t *partition);
```

此函数设置下次启动时使用的分区。

- `partition`：要设置为启动分区的分区

#### 中止 OTA 升级

```c
esp_err_t esp_ota_abort(esp_ota_handle_t handle);
```

此函数中止 OTA 升级过程，释放相关资源。

- `handle`：由 `esp_ota_begin` 返回的句柄

### 4.3 OTA 验证与回滚

#### 获取 OTA 分区状态

```c
esp_err_t esp_ota_get_state_partition(const esp_partition_t *partition, esp_ota_img_states_t *ota_state);
```

此函数获取指定 OTA 分区的状态。

- `partition`：要查询的分区
- `ota_state`：输出参数，分区状态

#### 标记当前固件为有效

```c
esp_err_t esp_ota_mark_app_valid_cancel_rollback(void);
```

此函数将当前运行的固件标记为有效，取消可能的回滚。通常在确认新固件正常运行后调用。

#### 标记当前固件为无效

```c
esp_err_t esp_ota_mark_app_invalid_rollback_and_reboot(void);
```

此函数将当前运行的固件标记为无效，触发回滚并重启设备。通常在检测到新固件问题时调用。

## 5. 完整示例

### 5.1 HTTP OTA 示例

以下是一个基于 HTTP 的 OTA 升级完整示例：

```c
#include <string.h>
#include <sys/socket.h>
#include <netdb.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_system.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_ota_ops.h"
#include "esp_http_client.h"
#include "esp_flash_partitions.h"
#include "esp_partition.h"
#include "nvs.h"
#include "nvs_flash.h"
#include "driver/gpio.h"
#include "protocol_examples_common.h"

#define BUFFSIZE 1024
#define HASH_LEN 32

static const char *TAG = "OTA_EXAMPLE";
static char ota_write_data[BUFFSIZE + 1] = { 0 };

extern const uint8_t server_cert_pem_start[] asm("_binary_ca_cert_pem_start");
extern const uint8_t server_cert_pem_end[] asm("_binary_ca_cert_pem_end");

static esp_err_t validate_image_header(esp_app_desc_t *new_app_info)
{
    if (new_app_info == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    const esp_partition_t *running = esp_ota_get_running_partition();
    esp_app_desc_t running_app_info;
    if (esp_ota_get_partition_description(running, &running_app_info) == ESP_OK) {
        ESP_LOGI(TAG, "Running firmware version: %s", running_app_info.version);
    }

    ESP_LOGI(TAG, "New firmware version: %s", new_app_info->version);

    // 检查版本号，确保新固件版本高于当前版本
    if (memcmp(new_app_info->version, running_app_info.version, sizeof(new_app_info->version)) == 0) {
        ESP_LOGW(TAG, "Current running version is the same as the new version. We will not continue the update.");
        return ESP_FAIL;
    }

    return ESP_OK;
}

void ota_task(void *pvParameter)
{
    esp_err_t err;
    esp_ota_handle_t update_handle = 0;
    const esp_partition_t *update_partition = NULL;

    ESP_LOGI(TAG, "Starting OTA example");

    const esp_partition_t *configured = esp_ota_get_boot_partition();
    const esp_partition_t *running = esp_ota_get_running_partition();

    if (configured != running) {
        ESP_LOGW(TAG, "Configured OTA boot partition at offset 0x%08x, but running from offset 0x%08x",
                 configured->address, running->address);
        ESP_LOGW(TAG, "(This can happen if either the OTA boot data or preferred boot image become corrupted somehow.)");
    }
    ESP_LOGI(TAG, "Running partition type %d subtype %d (offset 0x%08x)",
             running->type, running->subtype, running->address);

    // 配置HTTP客户端
    esp_http_client_config_t config = {
        .url = "http://example.com/firmware.bin",
        .cert_pem = (char *)server_cert_pem_start,
        .timeout_ms = 5000,
    };
    esp_http_client_handle_t client = esp_http_client_init(&config);
    if (client == NULL) {
        ESP_LOGE(TAG, "Failed to initialize HTTP client");
        vTaskDelete(NULL);
    }

    err = esp_http_client_open(client, 0);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to open HTTP connection: %s", esp_err_to_name(err));
        esp_http_client_cleanup(client);
        vTaskDelete(NULL);
    }

    esp_http_client_fetch_headers(client);
    int binary_file_length = esp_http_client_get_content_length(client);
    if (binary_file_length <= 0) {
        ESP_LOGE(TAG, "Invalid content length");
        esp_http_client_cleanup(client);
        vTaskDelete(NULL);
    }

    update_partition = esp_ota_get_next_update_partition(NULL);
    if (update_partition == NULL) {
        ESP_LOGE(TAG, "Failed to get update partition");
        esp_http_client_cleanup(client);
        vTaskDelete(NULL);
    }
    ESP_LOGI(TAG, "Writing to partition subtype %d at offset 0x%x",
             update_partition->subtype, update_partition->address);

    int data_read;
    bool image_header_was_checked = false;
    
    // 开始OTA过程
    err = esp_ota_begin(update_partition, OTA_WITH_SEQUENTIAL_WRITES, &update_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "esp_ota_begin failed (%s)", esp_err_to_name(err));
        esp_http_client_cleanup(client);
        vTaskDelete(NULL);
    }
    ESP_LOGI(TAG, "esp_ota_begin succeeded");

    // 读取固件数据并写入OTA分区
    int total_read = 0;
    while (1) {
        data_read = esp_http_client_read(client, ota_write_data, BUFFSIZE);
        if (data_read < 0) {
            ESP_LOGE(TAG, "Error: SSL data read error");
            break;
        } else if (data_read == 0) {
            ESP_LOGI(TAG, "Connection closed, all data received");
            break;
        }

        // 检查固件头信息
        if (image_header_was_checked == false) {
            esp_app_desc_t new_app_info;
            if (data_read > sizeof(esp_image_header_t) + sizeof(esp_image_segment_header_t) + sizeof(esp_app_desc_t)) {
                // 检查固件头信息
                memcpy(&new_app_info, &ota_write_data[sizeof(esp_image_header_t) + sizeof(esp_image_segment_header_t)], sizeof(esp_app_desc_t));
                err = validate_image_header(&new_app_info);
                if (err != ESP_OK) {
                    ESP_LOGE(TAG, "image header verification failed");
                    break;
                }
                image_header_was_checked = true;
            }
        }

        // 写入固件数据
        err = esp_ota_write(update_handle, (const void *)ota_write_data, data_read);
        if (err != ESP_OK) {
            ESP_LOGE(TAG, "Error: esp_ota_write failed (%s)!", esp_err_to_name(err));
            break;
        }
        total_read += data_read;
        ESP_LOGI(TAG, "Written %d/%d bytes", total_read, binary_file_length);
    }

    // 清理HTTP客户端
    esp_http_client_cleanup(client);

    // 完成OTA过程
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "OTA failed");
        esp_ota_abort(update_handle);
        vTaskDelete(NULL);
    }

    err = esp_ota_end(update_handle);
    if (err != ESP_OK) {
        if (err == ESP_ERR_OTA_VALIDATE_FAILED) {
            ESP_LOGE(TAG, "Image validation failed, image is corrupted");
        } else {
            ESP_LOGE(TAG, "esp_ota_end failed (%s)!", esp_err_to_name(err));
        }
        vTaskDelete(NULL);
    }

    // 设置启动分区为新固件分区
    err = esp_ota_set_boot_partition(update_partition);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "esp_ota_set_boot_partition failed (%s)!", esp_err_to_name(err));
        vTaskDelete(NULL);
    }
    ESP_LOGI(TAG, "OTA complete, rebooting...");
    esp_restart();

    vTaskDelete(NULL);
}

void app_main(void)
{
    // 初始化NVS
    esp_err_t err = nvs_flash_init();
    if (err == ESP_ERR_NVS_NO_FREE_PAGES || err == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        err = nvs_flash_init();
    }
    ESP_ERROR_CHECK(err);

    // 初始化网络
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    ESP_ERROR_CHECK(example_connect());

    // 检查当前固件状态，如果是待验证状态，标记为有效
    const esp_partition_t *running = esp_ota_get_running_partition();
    esp_ota_img_states_t ota_state;
    if (esp_ota_get_state_partition(running, &ota_state) == ESP_OK) {
        if (ota_state == ESP_OTA_IMG_PENDING_VERIFY) {
            // 固件验证成功，标记为有效
            ESP_LOGI(TAG, "Diagnostics completed successfully! Marking firmware as valid.");
            esp_ota_mark_app_valid_cancel_rollback();
        }
    }

    // 创建OTA任务
    xTaskCreate(&ota_task, "ota_task", 8192, NULL, 5, NULL);
}
```

### 5.2 HTTPS OTA 示例

如果需要通过 HTTPS 进行 OTA 升级，只需修改 HTTP 客户端配置：

```c
esp_http_client_config_t config = {
    .url = "https://example.com/firmware.bin",
    .cert_pem = (char *)server_cert_pem_start,
    .timeout_ms = 5000,
};
```

并确保包含了服务器证书：

```c
extern const uint8_t server_cert_pem_start[] asm("_binary_ca_cert_pem_start");
extern const uint8_t server_cert_pem_end[] asm("_binary_ca_cert_pem_end");
```

## 6. 安全性考虑

在实现 OTA 升级时，应考虑以下安全问题：

1. **使用 HTTPS**：通过 HTTPS 传输固件，防止中间人攻击
2. **固件签名**：使用数字签名验证固件的来源和完整性
3. **版本验证**：确保新固件版本高于当前版本
4. **固件加密**：加密固件内容，防止未授权访问
5. **安全启动**：使用安全启动功能验证固件
6. **回滚保护**：防止回滚到存在安全漏洞的旧版本

ESP-IDF 提供了 `esp_https_ota` 组件，集成了 HTTPS 下载和 OTA 升级功能，并支持固件签名验证。

## 7. 常见问题与解决方案

### 问题：OTA 升级失败，返回 ESP_ERR_OTA_VALIDATE_FAILED

**解决方案**：
- 确保下载的固件完整，没有被截断或损坏
- 检查固件是否为有效的 ESP32 固件格式
- 验证固件是否与目标设备兼容

### 问题：OTA 升级后设备无法启动

**解决方案**：
- 实现回滚机制，在新固件启动失败时自动回滚到旧固件
- 在新固件中添加自检代码，确保关键功能正常工作
- 使用 `esp_ota_mark_app_valid_cancel_rollback()` 仅在确认固件正常工作后调用

### 问题：OTA 升级过程中设备重启或断电

**解决方案**：
- 双分区设计确保原固件不受影响
- 重启后设备仍会从原固件启动
- 可以实现断点续传功能，继续未完成的升级

### 问题：内存不足导致 OTA 失败

**解决方案**：
- 减小缓冲区大小，分多次读取和写入固件
- 优化内存使用，释放不必要的资源
- 考虑使用 SPIRAM 扩展内存

## 8. 参考资料

- [ESP-IDF 编程指南 - OTA 升级](https://docs.espressif.com/projects/esp-idf/zh_CN/latest/esp32/api-reference/system/ota.html)
- [ESP-IDF 示例 - OTA 升级](https://github.com/espressif/esp-idf/tree/master/examples/system/ota)
- [ESP-IDF 编程指南 - 分区表](https://docs.espressif.com/projects/esp-idf/zh_CN/latest/esp32/api-guides/partition-tables.html)
- [ESP-IDF 编程指南 - 安全启动](https://docs.espressif.com/projects/esp-idf/zh_CN/latest/esp32/security/secure-boot-v2.html)
- [ESP-IDF 编程指南 - Flash 加密](https://docs.espressif.com/projects/esp-idf/zh_CN/latest/esp32/security/flash-encryption.html)
