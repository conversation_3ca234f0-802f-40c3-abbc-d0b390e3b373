ESP32 BLE Provisioning 配网协议文档
1. 概述
本文档详细描述了基于 ESP32 的 BLE Provisioning 配网协议，用于与微信小程序进行对接。该协议遵循 ESP-IDF 标准 BLE Provisioning 规范，确保与官方微信小程序的兼容性。

2. 技术架构
2.1 协议栈结构
微信小程序
    ↓
BLE GATT 协议
    ↓
ESP32 BLE Provisioning Service
    ↓
WiFi 配置管理
    ↓
设备网络连接
2.2 核心组件
BLE Service: 标准 ESP BLE Provisioning 服务
Security: WIFI_PROV_SECURITY_1 安全级别
Transport: BLE GATT 传输层
Protocol: Protobuf 消息协议
3. BLE 服务规范
3.1 服务 UUID
// 标准ESP BLE Provisioning服务UUID (128位)
#define PROV_BLE_SERVICE_UUID_128   {0xb4, 0xdf, 0x5a, 0x1c, 0x3f, 0x6b, 0xf4, 0xbf, \
                                     0xea, 0x4a, 0x82, 0x03, 0x04, 0x90, 0x1a, 0x02}
3.2 GATT 特征值
ESP BLE Provisioning 使用以下标准特征值：

特征值名称	UUID	属性	描述
prov-session-state	自动分配	Read/Write	会话状态管理
prov-session-setup	自动分配	Read/Write	会话建立
prov-config	自动分配	Read/Write	WiFi 配置
proto-ver	自动分配	Read	协议版本
4. 设备端实现
4.1 初始化配置
bool ble_prov_start(const char *device_name, const char *pop, bool wait_for_completion)
{
    // 配置标准ESP BLE Provisioning
    wifi_prov_mgr_config_t config = {
        .scheme = wifi_prov_scheme_ble,
        .scheme_event_handler = WIFI_PROV_SCHEME_BLE_EVENT_HANDLER_FREE_BTDM
    };
    
    // 启动标准BLE配网服务
    ret = wifi_prov_mgr_start_provisioning(WIFI_PROV_SECURITY_1, 
                                         (const void*)pop, 
                                         device_name, NULL);
}
4.2 设备命名规则
main/boards/common
// 获取MAC地址并创建唯一设备名称
uint8_t mac[6];
esp_read_mac(mac, ESP_MAC_WIFI_STA);
char device_name[32];
sprintf(device_name, "PROV_%02X%02X%02X", mac[3], mac[4], mac[5]);
设备名称格式: PROV_XXXXXX

前缀: PROV_
后缀: MAC 地址后3字节的十六进制表示
示例: PROV_6916EC
4.3 安全配置
安全级别:  WIFI_PROV_SECURITY_1
默认 POP: 123456
加密: 使用 Curve25519 密钥交换和 AES-CTR 加密
5. 二维码规范
5.1 二维码数据格式
{
  "ver": "v1",
  "name": "PROV_6916EC",
  "pop": "123456",
  "transport": "ble"
}
5.2 字段说明
字段	类型	必需	描述
ver	string	是	协议版本，固定为 "v1"
name	string	是	BLE 设备广播名称
pop	string	是	安全密钥 (Proof of Possession)
transport	string	是	传输方式，固定为 "ble"
5.3 二维码生成参数
错误纠正级别: LOW (提高扫描成功率)
版本: 自适应 (3-6)
编码: UTF-8
掩码: AUTO
6. 配网流程
6.1 完整配网时序图
设备端                    微信小程序                  WiFi路由器
  |                          |                          |
  |-- 1. 启动BLE广播 -------->|                          |
  |                          |                          |
  |<-- 2. 扫描并连接 ---------|                          |
  |                          |                          |
  |<-- 3. 安全握手 ----------|                          |
  |-- 4. 握手响应 ----------->|                          |
  |                          |                          |
  |<-- 5. 发送WiFi凭证 ------|                          |
  |                          |                          |
  |-- 6. 尝试连接WiFi ----------------------->|          |
  |<-- 7. 连接结果 <-----------------------|          |
  |                          |                          |
  |-- 8. 反馈连接状态 ------->|                          |
  |                          |                          |
  |-- 9. 停止BLE服务 ------->|                          |
6.2 详细步骤说明
步骤1: 设备启动BLE广播
// 设备开始广播，广播名称为 PROV_XXXXXX
wifi_prov_mgr_start_provisioning(WIFI_PROV_SECURITY_1, pop, device_name, NULL);
步骤2: 小程序扫描连接
小程序扫描 BLE 设备
识别以 PROV_ 开头的设备
建立 GATT 连接
步骤3-4: 安全握手
使用 WIFI_PROV_SECURITY_1 进行安全握手
验证 POP (Proof of Possession)
建立加密通道
步骤5: 发送WiFi凭证
小程序通过加密通道发送WiFi信息：

message WiFiConfigPayload {
    string ssid = 1;
    string passphrase = 2;
}
步骤6-7: WiFi连接测试
设备尝试连接指定WiFi网络并返回结果

步骤8: 状态反馈
设备向小程序反馈最终连接状态

步骤9: 清理资源
配网完成后停止BLE服务

7. 事件处理
7.1 主要事件类型
static void prov_event_handler(void* handler_arg, esp_event_base_t event_base, 
                             int32_t event_id, void* event_data)
{
    if (event_base == WIFI_PROV_EVENT) {
        switch (event_id) {
            case WIFI_PROV_START:
                // BLE配网服务已启动
                break;
            case WIFI_PROV_CRED_RECV:
                // 收到WiFi凭证
                break;
            case WIFI_PROV_CRED_SUCCESS:
                // WiFi连接成功
                break;
            case WIFI_PROV_CRED_FAIL:
                // WiFi连接失败
                break;
            case WIFI_PROV_END:
                // BLE配网结束
                break;
        }
    }
}

7.2 事件详细说明
事件	触发时机	数据内容	处理动作
WIFI_PROV_START	BLE服务启动	无	显示配网提示
WIFI_PROV_CRED_RECV	收到WiFi凭证	wifi_sta_config_t	记录凭证信息
WIFI_PROV_CRED_SUCCESS	WiFi连接成功	无	保存凭证到NVS
WIFI_PROV_CRED_FAIL	WiFi连接失败	失败原因	错误处理
WIFI_PROV_END	配网流程结束	无	清理资源
8. 微信小程序对接指南
8.1 小程序权限配置
在 app.json 中添加蓝牙权限：

{
  "permission": {
    "scope.bluetooth": {
      "desc": "用于设备配网"
    }
  },
  "requiredBackgroundModes": ["bluetooth-central"]
}
8.2 BLE扫描代码示例
// 开始扫描BLE设备
wx.startBluetoothDevicesDiscovery({
  services: [], // 可以指定服务UUID
  success: function(res) {
    console.log('开始扫描BLE设备');
  }
});

// 监听设备发现
wx.onBluetoothDeviceFound(function(devices) {
  devices.devices.forEach(device => {
    // 查找以PROV_开头的设备
    if (device.name && device.name.startsWith('PROV_')) {
      console.log('发现配网设备:', device.name);
      // 连接设备
      connectToDevice(device.deviceId);
    }
  });
});

8.3 GATT连接示例
function connectToDevice(deviceId) {
  wx.createBLEConnection({
    deviceId: deviceId,
    success: function(res) {
      console.log('连接成功');
      // 获取服务
      getBLEDeviceServices(deviceId);
    }
  });
}

function getBLEDeviceServices(deviceId) {
  wx.getBLEDeviceServices({
    deviceId: deviceId,
    success: function(res) {
      res.services.forEach(service => {
        // 查找ESP Provisioning服务
        if (service.uuid.includes('021a9004')) {
          console.log('找到配网服务');
          getBLEDeviceCharacteristics(deviceId, service.uuid);
        }
      });
    }
  });
}
8.4 数据传输示例
function sendWiFiCredentials(deviceId, serviceId, characteristicId, ssid, password) {
  // 构造WiFi凭证数据包
  const credentials = {
    ssid: ssid,
    passphrase: password
  };
  
  // 使用protobuf编码（需要引入protobuf.js）
  const buffer = encodeCredentials(credentials);
  
  wx.writeBLECharacteristicValue({
    deviceId: deviceId,
    serviceId: serviceId,
    characteristicId: characteristicId,
    value: buffer,
    success: function(res) {
      console.log('WiFi凭证发送成功');
    }
  });
}
9. 错误处理
9.1 常见错误码
错误码	描述	解决方案
ESP_ERR_WIFI_NOT_INIT	WiFi未初始化	检查WiFi初始化流程
ESP_ERR_NO_MEM	内存不足	增加堆内存配置
ESP_ERR_INVALID_ARG	参数无效	检查传入参数
ESP_ERR_WIFI_TIMEOUT	连接超时	检查WiFi信号和密码
9.2 超时配置
#define PROVISIONING_TIMEOUT_MS (120 * 1000) // 120秒超时
9.3 重试机制
配网失败后自动重试
最大重试次数: 3次
重试间隔: 5秒
10. 安全考虑
10.1 数据加密
使用 Curve25519 进行密钥交换
AES-CTR 模式加密数据传输
POP 验证防止未授权访问
10.2 安全建议
POP 密钥管理: 建议使用随机生成的POP，避免使用固定值
超时控制: 设置合理的配网超时时间
状态验证: 验证每个配网步骤的状态
错误处理: 妥善处理各种异常情况
11. 调试指南
11.1 日志级别配置
// 在menuconfig中设置
CONFIG_LOG_DEFAULT_LEVEL_DEBUG=y
CONFIG_LOG_MAXIMUM_LEVEL=4
11.2 关键日志监控
ESP_LOGI(TAG, "BLE配网服务已启动，设备名: %s", device_name);
ESP_LOGI(TAG, "收到WiFi凭证: SSID=%s", wifi_sta_cfg->ssid);
ESP_LOGI(TAG, "WiFi连接成功");
ESP_LOGE(TAG, "配网失败或超时");
11.3 常用调试命令
# 监控串口日志
idf.py monitor

# 查看BLE广播
hcitool lescan

# 检查WiFi状态
idf.py monitor | grep WIFI
12. 性能优化
12.1 内存优化
及时释放BLE资源
优化缓冲区大小
避免内存泄漏
12.2 功耗优化
配网完成后及时关闭BLE
使用低功耗模式
优化广播间隔
12.3 连接优化
合理设置连接参数
优化数据传输大小
减少重连次数
13. 兼容性说明
13.1 ESP-IDF版本
最低版本: ESP-IDF v4.4
推荐版本: ESP-IDF v5.0+
测试版本: ESP-IDF v5.3
13.2 微信小程序版本
基础库版本: 2.9.0+
支持的微信版本: 7.0.0+
13.3 设备兼容性
ESP32 系列: 完全支持
ESP32-S2: 不支持（无蓝牙）
ESP32-S3: 完全支持
ESP32-C3: 完全支持
14. 示例代码
14.1 完整的设备端代码
参考  main/ble_provisioning.cc 文件中的实现

14.2 微信小程序示例
建议参考 ESP-IDF 官方提供的微信小程序示例或使用现有的 ESP BLE Provisioning 小程序

15. 常见问题 FAQ
Q1: 设备无法被小程序发现？
A: 检查设备名称是否以 PROV_ 开头，确认BLE广播已启动

Q2: 连接后无法发送数据？
A: 确认安全握手是否完成，检查POP密钥是否正确

Q3: WiFi连接失败？
A: 检查WiFi密码是否正确，信号强度是否足够

Q4: 配网超时？
A: 增加超时时间，检查网络环境

Q5: 内存不足错误？
A: 增加堆内存配置，及时释放不用的资源

本文档提供了完整的 ESP32 BLE Provisioning 配网协议实现指南，可以直接用于与微信小程序的对接开发