# ESP32 BLE 配网模块整改建议文档

> 项目目标：实现微信小程序通过 BLE 与 ESP32 设备连接，并完成 Wi-Fi 配网。
>
> 当前使用开源代码仓库：[https://github.com/78/xiaozhi-esp32](https://github.com/78/xiaozhi-esp32) 小程序已兼容 ESP-IDF 官方 BLE Provisioning 协议，但设备端连接失败（报错 status:133）。本整改文档面向设备端（esp32）嵌入式工程师，汇总分析结论与待整改项。

---

## 一、现状概述

- ESP32 固件使用了 ESP-IDF 官方 BLE Provisioning 方案：`wifi_provisioning/manager.h + scheme_ble.h`
- 小程序端已完整支持标准 UUID 和协议帧格式；BLE 扫描正常，但 BLE 建立连接失败。
- 微信小程序报错：`createBLEConnection:fail:connection fail status:133`
- 这是典型 BLE GATT 服务未就绪或广播未完成的错误（连接前设备未准备好）。

---

## 二、核心风险点分析（来自 esp32.zip 源码）

### ✅ BLE 使用了官方配网模块：

```c
wifi_prov_mgr_start_provisioning(... scheme_ble ...);
```

模块内部创建 GATT 服务并启动广播（无需手动配置 UUID）。

### ❗风险点 1：BLE 启动时机可能过早

- 建议仅在完成 NVS 初始化、WiFi 初始化、事件注册之后再调用 `ble_prov_start(...)`
- 若系统尚未准备好，BLE 服务可能未注册成功，导致小程序连接失败

### ❗风险点 2：BLE 广播可能被自动关闭

- 当设备配网成功或进入其他状态时，系统默认会关闭 BLE 广播，导致微信小程序无法连接

---

## 三、整改建议（请嵌入式开发落实）

### ✅ 建议 1：添加 BLE 启动延时（调试用）

在 `main()` 或 `app_main()` 中添加如下延迟逻辑，确保 BLE 服务在系统初始化完成后再启动：

```c
vTaskDelay(pdMS_TO_TICKS(5000));  // 延迟5秒
ble_prov_start("PROV_XXXX", "123456", false);
```

### ✅ 建议 2：关闭 BLE 自动停止（强制持续广播）

添加如下代码防止广播因自动停止而断开：

```c
wifi_prov_mgr_disable_auto_stop(1);  // 禁用自动关闭BLE广播
```

### ✅ 建议 3：加强 BLE 启动日志输出（便于诊断）

建议添加如下日志确认服务是否成功启动：

```c
ESP_LOGI(TAG, "[BLE] Starting BLE provisioning...");
ESP_LOGI(TAG, "[BLE] Advertising started");
```

### ✅ 建议 4：使用 Android 手机 + nRF Connect 工具调试

在微信小程序之外使用 BLE 工具观察：

- 广播是否持续存在？
- 是否含有服务 UUID：`1775244D-6B43-439B-877C-060F2D9BED07`
- 是否能连接成功？（避免被其他手机/调试工具占用连接）

---

## 四、main.c 推荐初始化顺序（参考）

```c
void app_main(void) {
    // 初始化 NVS
    nvs_flash_init();

    // 初始化网络栈 & 事件循环
    esp_netif_init();
    esp_event_loop_create_default();

    // 初始化 WiFi
    wifi_init();

    // 等待几秒，确保系统服务启动完成
    vTaskDelay(pdMS_TO_TICKS(3000));

    // 启动 BLE 配网
    wifi_prov_mgr_disable_auto_stop(1);
    ble_prov_start("PROV_XXXX", "123456", false);
}
```

---

## 五、结语

BLE 状态码 133 在 Android BLE 栈和微信小程序 BLE 框架中是**连接失败的通用错误码**，大多数情况是设备未准备好导致的临时连接失败。

请确保：

- BLE 广播一直存在且包含服务 UUID；
- BLE 服务在启动配网前已就绪；
- 设备未被其他客户端连接；
- 小程序连接前有适当延时或重试。

整改完成后，请配合小程序端开发再次测试 BLE 扫描 + 连接 + 配网流程。

如有其他 BLE 驱动或 RTOS 任务冲突问题，也建议集中排查（特别是省电模式、BLE 被抢占等）。

