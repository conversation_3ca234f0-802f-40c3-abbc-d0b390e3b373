#include "ble_provisioning.h"

#include <string.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/event_groups.h>
#include <esp_log.h>
#include <esp_wifi.h>
#include <esp_event.h>
#include <esp_timer.h>
#include <esp_system.h>
#include <esp_bt.h>
#include <esp_gap_ble_api.h>
#include <esp_gatts_api.h>
#include <esp_bt_main.h>
#include <esp_gatt_common_api.h>
#include <nvs_flash.h>
#include "qrcode.h"
#include "ssid_manager.h"
#include "wifi_station.h"
#include <cJSON.h>

static const char *TAG = "BLE_PROV";

// 简化的BLE服务UUID (自定义)
#define SIMPLE_PROV_SERVICE_UUID        0x1234
#define SIMPLE_PROV_CHAR_WIFI_UUID      0x5678

// 事件组位标志
#define WIFI_PROV_SUCCESS_BIT  BIT0
#define WIFI_PROV_FAIL_BIT      BIT1
#define PROVISIONING_TIMEOUT_MS (120 * 1000)

static EventGroupHandle_t wifi_prov_event_group = NULL;
static bool provisioning_successful = false;
static wifi_sta_config_t received_wifi_cfg;

// BLE相关变量
static uint16_t gatts_if = ESP_GATT_IF_NONE;
static uint16_t conn_id = 0;
static uint16_t service_handle = 0;
static uint16_t char_handle = 0;
static bool ble_connected = false;

// 解析WiFi配置的多种格式
static bool parse_wifi_config(const char* data, size_t len, char* ssid, char* password) {
    ESP_LOGI(TAG, "[BLE] 解析WiFi配置数据，长度: %d", len);
    ESP_LOGI(TAG, "[BLE] 原始数据: %.*s", len, data);
    
    // 方案1: JSON格式 {"ssid":"xxx","password":"xxx"}
    cJSON *json = cJSON_Parse(data);
    if (json) {
        cJSON *ssid_item = cJSON_GetObjectItem(json, "ssid");
        cJSON *pwd_item = cJSON_GetObjectItem(json, "password");
        if (ssid_item && pwd_item && cJSON_IsString(ssid_item) && cJSON_IsString(pwd_item)) {
            strncpy(ssid, ssid_item->valuestring, 32);
            strncpy(password, pwd_item->valuestring, 64);
            cJSON_Delete(json);
            ESP_LOGI(TAG, "[BLE] JSON格式解析成功: SSID=%s", ssid);
            return true;
        }
        cJSON_Delete(json);
    }
    
    // 方案2: 简单文本格式 "ssid,password"
    char* comma = strchr(data, ',');
    if (comma && (comma - data) < 32) {
        size_t ssid_len = comma - data;
        size_t pwd_len = len - ssid_len - 1;
        if (pwd_len < 64) {
            strncpy(ssid, data, ssid_len);
            ssid[ssid_len] = '\0';
            strncpy(password, comma + 1, pwd_len);
            password[pwd_len] = '\0';
            ESP_LOGI(TAG, "[BLE] 文本格式解析成功: SSID=%s", ssid);
            return true;
        }
    }
    
    // 方案3: 纯SSID (无密码的开放网络)
    if (len < 32) {
        strncpy(ssid, data, len);
        ssid[len] = '\0';
        password[0] = '\0';
        ESP_LOGI(TAG, "[BLE] 纯SSID格式解析成功: SSID=%s (开放网络)", ssid);
        return true;
    }
    
    ESP_LOGE(TAG, "[BLE] 无法解析WiFi配置数据");
    return false;
}

// GATT事件处理
static void gatts_event_handler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if_param, esp_ble_gatts_cb_param_t *param) {
    switch (event) {
        case ESP_GATTS_REG_EVT: {
            ESP_LOGI(TAG, "[BLE] GATT服务注册成功");
            gatts_if = gatts_if_param;
            
            // 创建服务
            esp_gatt_srvc_id_t service_id = {};
            service_id.is_primary = true;
            service_id.id.inst_id = 0;
            service_id.id.uuid.len = ESP_UUID_LEN_16;
            service_id.id.uuid.uuid.uuid16 = SIMPLE_PROV_SERVICE_UUID;
            esp_ble_gatts_create_service(gatts_if, &service_id, 4);
            break;
        }
            
        case ESP_GATTS_CREATE_EVT: {
            ESP_LOGI(TAG, "[BLE] 服务创建成功");
            service_handle = param->create.service_handle;
            
            // 添加特征值
            esp_bt_uuid_t char_uuid = {};
            char_uuid.len = ESP_UUID_LEN_16;
            char_uuid.uuid.uuid16 = SIMPLE_PROV_CHAR_WIFI_UUID;
            
            esp_ble_gatts_add_char(service_handle, &char_uuid,
                ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                ESP_GATT_CHAR_PROP_BIT_READ | ESP_GATT_CHAR_PROP_BIT_WRITE,
                NULL, NULL);
            break;
        }
            
        case ESP_GATTS_ADD_CHAR_EVT: {
            ESP_LOGI(TAG, "[BLE] 特征值添加成功");
            char_handle = param->add_char.attr_handle;
            
            // 启动服务
            esp_ble_gatts_start_service(service_handle);
            break;
        }
            
        case ESP_GATTS_START_EVT: {
            ESP_LOGI(TAG, "[BLE] 服务启动成功，开始广播");
            
            // 设置广播数据
            uint16_t service_uuid = SIMPLE_PROV_SERVICE_UUID;
            esp_ble_adv_data_t adv_data = {};
            adv_data.set_scan_rsp = false;
            adv_data.include_name = true;
            adv_data.include_txpower = false;
            adv_data.min_interval = 0x0006;
            adv_data.max_interval = 0x0010;
            adv_data.appearance = 0x00;
            adv_data.manufacturer_len = 0;
            adv_data.p_manufacturer_data = NULL;
            adv_data.service_data_len = 0;
            adv_data.p_service_data = NULL;
            adv_data.service_uuid_len = sizeof(uint16_t);
            adv_data.p_service_uuid = (uint8_t*)&service_uuid;
            adv_data.flag = (ESP_BLE_ADV_FLAG_GEN_DISC | ESP_BLE_ADV_FLAG_BREDR_NOT_SPT);
            esp_ble_gap_config_adv_data(&adv_data);
            break;
        }
            
        case ESP_GATTS_CONNECT_EVT: {
            ESP_LOGI(TAG, "[BLE] 🔗 客户端已连接");
            conn_id = param->connect.conn_id;
            ble_connected = true;
            break;
        }
            
        case ESP_GATTS_DISCONNECT_EVT: {
            ESP_LOGI(TAG, "[BLE] 🔌 客户端已断开连接");
            ble_connected = false;
            // 重新开始广播
            esp_ble_adv_params_t adv_params = {};
            adv_params.adv_int_min = 0x20;
            adv_params.adv_int_max = 0x40;
            adv_params.adv_type = ADV_TYPE_IND;
            adv_params.own_addr_type = BLE_ADDR_TYPE_PUBLIC;
            adv_params.channel_map = ADV_CHNL_ALL;
            adv_params.adv_filter_policy = ADV_FILTER_ALLOW_SCAN_ANY_CON_ANY;
            esp_ble_gap_start_advertising(&adv_params);
            break;
        }
            
        case ESP_GATTS_WRITE_EVT: {
            if (param->write.handle == char_handle) {
                ESP_LOGI(TAG, "[BLE] 📥 接收到WiFi配置数据");
                
                char ssid[33] = {0};
                char password[65] = {0};
                
                if (parse_wifi_config((char*)param->write.value, param->write.len, ssid, password)) {
                    // 保存WiFi凭证
                    strncpy((char*)received_wifi_cfg.ssid, ssid, sizeof(received_wifi_cfg.ssid));
                    strncpy((char*)received_wifi_cfg.password, password, sizeof(received_wifi_cfg.password));
                    
                    auto& ssid_manager = SsidManager::GetInstance();
                    ssid_manager.AddSsid(ssid, password);
                    
                    ESP_LOGI(TAG, "[BLE] 💾 WiFi凭证已保存: SSID=%s", ssid);
                    
                    // 尝试连接WiFi
                    esp_wifi_set_mode(WIFI_MODE_STA);
                    esp_wifi_set_config(WIFI_IF_STA, (wifi_config_t*)&received_wifi_cfg);
                    esp_wifi_start();
                    esp_wifi_connect();
                    
                    // 发送成功响应
                    const char* response = "OK";
                    esp_ble_gatts_send_indicate(gatts_if, conn_id, char_handle, 
                                              strlen(response), (uint8_t*)response, false);
                    
                    provisioning_successful = true;
                    if (wifi_prov_event_group) {
                        xEventGroupSetBits(wifi_prov_event_group, WIFI_PROV_SUCCESS_BIT);
                    }
                } else {
                    // 发送错误响应
                    const char* response = "ERROR";
                    esp_ble_gatts_send_indicate(gatts_if, conn_id, char_handle, 
                                              strlen(response), (uint8_t*)response, false);
                }
            }
            break;
        }
            
        default:
            break;
    }
}

// GAP事件处理
static void gap_event_handler(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t *param) {
    switch (event) {
        case ESP_GAP_BLE_ADV_DATA_SET_COMPLETE_EVT: {
            ESP_LOGI(TAG, "[BLE] 广播数据设置完成，开始广播");
            esp_ble_adv_params_t adv_params = {};
            adv_params.adv_int_min = 0x20;
            adv_params.adv_int_max = 0x40;
            adv_params.adv_type = ADV_TYPE_IND;
            adv_params.own_addr_type = BLE_ADDR_TYPE_PUBLIC;
            adv_params.channel_map = ADV_CHNL_ALL;
            adv_params.adv_filter_policy = ADV_FILTER_ALLOW_SCAN_ANY_CON_ANY;
            esp_ble_gap_start_advertising(&adv_params);
            break;
        }
            
        case ESP_GAP_BLE_ADV_START_COMPLETE_EVT: {
            if (param->adv_start_cmpl.status == ESP_BT_STATUS_SUCCESS) {
                ESP_LOGI(TAG, "[BLE] 📡 广播启动成功，等待连接");
            } else {
                ESP_LOGE(TAG, "[BLE] 广播启动失败");
            }
            break;
        }
            
        default:
            break;
    }
}

bool ble_prov_start(const char *device_name, const char *pop, bool wait_for_completion)
{
    ESP_LOGI(TAG, "[BLE] 启动简化BLE配网服务...");
    
    // 初始化蓝牙控制器
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    esp_err_t ret = esp_bt_controller_init(&bt_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "蓝牙控制器初始化失败: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "蓝牙控制器启用失败: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 初始化蓝牙栈
    ret = esp_bluedroid_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "蓝牙栈初始化失败: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_bluedroid_enable();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "蓝牙栈启用失败: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 设置设备名称
    esp_ble_gap_set_device_name(device_name);
    
    // 注册回调函数
    esp_ble_gatts_register_callback(gatts_event_handler);
    esp_ble_gap_register_callback(gap_event_handler);
    
    // 注册GATT应用
    esp_ble_gatts_app_register(0);
    
    // 创建事件组
    if (!wifi_prov_event_group) {
        wifi_prov_event_group = xEventGroupCreate();
        if (!wifi_prov_event_group) {
            ESP_LOGE(TAG, "创建事件组失败");
            return false;
        }
    }
    
    ESP_LOGI(TAG, "[BLE] ✅ 简化BLE配网服务已启动");
    ESP_LOGI(TAG, "[BLE] 📱 设备名称: %s", device_name);
    ESP_LOGI(TAG, "[BLE] 🔓 无加密模式，支持多种数据格式");
    ESP_LOGI(TAG, "[BLE] 📋 支持格式: JSON、文本(ssid,password)、纯SSID");
    
    // 生成BLE连接信息二维码
    print_ble_qrcode_to_terminal(device_name, "1234", "5678");
    
    if (wait_for_completion) {
        EventBits_t bits = xEventGroupWaitBits(wifi_prov_event_group,
                                              WIFI_PROV_SUCCESS_BIT | WIFI_PROV_FAIL_BIT,
                                              pdTRUE, pdFALSE,
                                              pdMS_TO_TICKS(PROVISIONING_TIMEOUT_MS));
        
        if (bits & WIFI_PROV_SUCCESS_BIT) {
            ESP_LOGI(TAG, "[BLE] 配网成功");
            return true;
        } else {
            ESP_LOGE(TAG, "[BLE] 配网失败或超时");
            return false;
        }
    }
    
    return true;
}




