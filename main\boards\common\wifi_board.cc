#include "wifi_board.h"

#include "display.h"
#include "application.h"
#include "system_info.h"
#include "font_awesome_symbols.h"
#include "settings.h"
#include "assets/lang_config.h"

#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <esp_http.h>
#include <esp_mqtt.h>
#include <esp_udp.h>
#include <tcp_transport.h>
#include <tls_transport.h>
#include <web_socket.h>
#include <esp_log.h>

#include <wifi_station.h>
#include <wifi_configuration_ap.h>
#include <ssid_manager.h>

// 添加蓝牙配网相关头文件
#include <nvs_flash.h>
#include <esp_efuse.h>
#include <esp_netif.h>
#include <ble_provisioning.h>
#include <esp_mac.h>
#include <wifi_provisioning/manager.h>


static const char *TAG = "WifiBoard";

WifiBoard::WifiBoard() {
    Settings settings("wifi", true);
    wifi_config_mode_ = settings.GetInt("force_ap") == 1;
    if (wifi_config_mode_) {
        ESP_LOGI(TAG, "force_ap is set to 1, reset to 0");
        settings.SetInt("force_ap", 0);
    }
}

std::string WifiBoard::GetBoardType() {
    return "wifi";
}


// 添加蓝牙配网函数
bool WifiBoard::TryBleProvisioning() {
    ESP_LOGI(TAG, "开始蓝牙配网过程（内存优化模式）...");
    
    // 清除所有已保存的WiFi网络
    auto& ssid_manager = SsidManager::GetInstance();
    ESP_LOGI(TAG, "清除所有已保存的WiFi网络");
    ssid_manager.Clear();
    
    // 清除配网管理器状态
    wifi_prov_mgr_reset_provisioning();
    
    // 确保ESP-NETIF已初始化
    esp_err_t ret = esp_netif_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "网络接口初始化失败: %s", esp_err_to_name(ret));
        return false;
    }

    // 尝试启动BLE配网
    if (ble_prov_start("PROV_DEVICE", "123456", true)) {
        ESP_LOGI(TAG, "BLE配网成功");
        // 配网成功后尝试连接WiFi
        if (StartWifiWithSavedCredentials()) {
            return true;
        }
    }
    
    ESP_LOGW(TAG, "BLE配网失败");
    return false;
}

// 新增：使用保存的凭证启动WiFi
bool WifiBoard::StartWifiWithSavedCredentials() {
    ESP_LOGI(TAG, "使用保存的凭证启动WiFi连接...");
    
    auto& ssid_manager = SsidManager::GetInstance();
    auto ssid_list = ssid_manager.GetSsidList();
    
    if (ssid_list.empty()) {
        ESP_LOGE(TAG, "没有保存的WiFi凭证");
        return false;
    }
    
    auto& wifi_station = WifiStation::GetInstance();
    
    // 设置连接回调
    wifi_station.OnConnect([this](const std::string& ssid) {
        auto display = Board::GetInstance().GetDisplay();
        std::string notification = Lang::Strings::CONNECT_TO + ssid + "...";
        display->ShowNotification(notification.c_str(), 30000);
    });
    
    wifi_station.OnConnected([this](const std::string& ssid) {
        auto display = Board::GetInstance().GetDisplay();
        std::string notification = Lang::Strings::CONNECTED_TO + ssid;
        display->ShowNotification(notification.c_str(), 30000);
    });
    
    // 启动WiFi连接
    wifi_station.Start();
    
    // 等待连接结果
    if (wifi_station.WaitForConnected(30 * 1000)) {
        ESP_LOGI(TAG, "WiFi连接成功");
        return true;
    } else {
        ESP_LOGE(TAG, "WiFi连接超时");
        wifi_station.Stop();
        return false;
    }
}



void WifiBoard::EnterWifiConfigMode() {
    auto& application = Application::GetInstance();
    application.SetDeviceState(kDeviceStateWifiConfiguring);

    auto& wifi_ap = WifiConfigurationAp::GetInstance();
    wifi_ap.SetLanguage(Lang::CODE);
    wifi_ap.SetSsidPrefix("Xiaozhi");
    wifi_ap.Start();

    // 显示 WiFi 配置 AP 的 SSID 和 Web 服务器 URL
    std::string hint = Lang::Strings::CONNECT_TO_HOTSPOT;
    hint += wifi_ap.GetSsid();
    hint += Lang::Strings::ACCESS_VIA_BROWSER;
    hint += wifi_ap.GetWebServerUrl();
    hint += "\n\n";
    
    // 播报配置 WiFi 的提示
    application.Alert(Lang::Strings::WIFI_CONFIG_MODE, hint.c_str(), "", Lang::Sounds::P3_WIFICONFIG);
    
    // Wait forever until reset after configuration
    while (true) {
        int free_sram = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
        int min_free_sram = heap_caps_get_minimum_free_size(MALLOC_CAP_INTERNAL);
        ESP_LOGI(TAG, "Free internal: %u minimal internal: %u", free_sram, min_free_sram);
        vTaskDelay(pdMS_TO_TICKS(10000));
    }
}

void WifiBoard::StartNetwork() {
    if (wifi_config_mode_) {
        // 强制配网模式：清除配置并重启
        auto& ssid_manager = SsidManager::GetInstance();
        ESP_LOGI(TAG, "强制配网模式：清除所有已保存的WiFi网络");
        ssid_manager.Clear();
        esp_restart();
        return;
    }

    // 检查是否有保存的WiFi配置
    auto& ssid_manager = SsidManager::GetInstance();
    auto ssid_list = ssid_manager.GetSsidList();
    
    if (ssid_list.empty()) {
        // 没有WiFi配置，启动BLE配网
        ESP_LOGI(TAG, "没有WiFi配置，启动BLE配网模式");
        wifi_config_mode_ = true;
        
        if (TryBleProvisioning()) {
            // BLE配网成功，WiFi已连接，继续启动应用
            ESP_LOGI(TAG, "BLE配网完成，继续启动应用");
            return;
        } else {
            // BLE配网失败，进入AP配网模式
            ESP_LOGW(TAG, "BLE配网失败，切换到AP配网模式");
            EnterWifiConfigMode();
            return;
        }
    }

    // 有WiFi配置，直接启动WiFi连接
    ESP_LOGI(TAG, "发现已保存的WiFi配置，直接连接");
    if (!StartWifiWithSavedCredentials()) {
        // WiFi连接失败，进入配网模式
        ESP_LOGW(TAG, "WiFi连接失败，进入配网模式");
        wifi_config_mode_ = true;
        EnterWifiConfigMode();
    }
}

Http* WifiBoard::CreateHttp() {
    return new EspHttp();
}

WebSocket* WifiBoard::CreateWebSocket() {
#ifdef CONFIG_CONNECTION_TYPE_WEBSOCKET
    std::string url = CONFIG_WEBSOCKET_URL;
    if (url.find("wss://") == 0) {
        return new WebSocket(new TlsTransport());
    } else {
        return new WebSocket(new TcpTransport());
    }
#endif
    return nullptr;
}

Mqtt* WifiBoard::CreateMqtt() {
    return new EspMqtt();
}

Udp* WifiBoard::CreateUdp() {
    return new EspUdp();
}

const char* WifiBoard::GetNetworkStateIcon() {
    if (wifi_config_mode_) {
        return FONT_AWESOME_WIFI;
    }
    auto& wifi_station = WifiStation::GetInstance();
    if (!wifi_station.IsConnected()) {
        return FONT_AWESOME_WIFI_OFF;
    }
    int8_t rssi = wifi_station.GetRssi();
    if (rssi >= -60) {
        return FONT_AWESOME_WIFI;
    } else if (rssi >= -70) {
        return FONT_AWESOME_WIFI_FAIR;
    } else {
        return FONT_AWESOME_WIFI_WEAK;
    }
}

std::string WifiBoard::GetBoardJson() {
    // Set the board type for OTA
    auto& wifi_station = WifiStation::GetInstance();
    std::string board_json = std::string("{\"type\":\"" BOARD_TYPE "\",");
    board_json += "\"name\":\"" BOARD_NAME "\",";
    if (!wifi_config_mode_) {
        board_json += "\"ssid\":\"" + wifi_station.GetSsid() + "\",";
        board_json += "\"rssi\":" + std::to_string(wifi_station.GetRssi()) + ",";
        board_json += "\"channel\":" + std::to_string(wifi_station.GetChannel()) + ",";
        board_json += "\"ip\":\"" + wifi_station.GetIpAddress() + "\",";
    }
    board_json += "\"mac\":\"" + SystemInfo::GetMacAddress() + "\"}";
    return board_json;
}

void WifiBoard::SetPowerSaveMode(bool enabled) {
    auto& wifi_station = WifiStation::GetInstance();
    wifi_station.SetPowerSaveMode(enabled);
}

void WifiBoard::ResetWifiConfiguration() {
    ESP_LOGI(TAG, "重置WiFi配置");
    auto& ssid_manager = SsidManager::GetInstance();
    ssid_manager.Clear();
    
    Settings settings("wifi", true);
    settings.SetInt("force_ap", 1);
    
    ESP_LOGI(TAG, "WiFi配置已重置，将重启设备");
    esp_restart();
}



