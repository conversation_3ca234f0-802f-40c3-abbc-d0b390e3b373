#ifndef WIFI_BOARD_H
#define WIFI_BOARD_H

#include "board.h"

class WifiBoard : public Board {
public:
    WifiBoard();
    std::string GetBoardType() override;
    void StartNetwork() override;
    bool TryBleProvisioning();
    bool StartWifiWithSavedCredentials();
    void ResetWifiConfiguration();  // 添加方法声明

    // 实现基类的纯虚函数
    Http* CreateHttp() override;
    WebSocket* CreateWebSocket() override;
    Mqtt* CreateMqtt() override;
    Udp* CreateUdp() override;
    const char* GetNetworkStateIcon() override;
    std::string GetBoardJson() override;
    void SetPowerSaveMode(bool enabled) override;

private:
    bool wifi_config_mode_;
    void EnterWifiConfigMode();
};

#endif // WIFI_BOARD_H


