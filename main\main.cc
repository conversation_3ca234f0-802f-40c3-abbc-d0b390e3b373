#include <esp_log.h>
#include <esp_err.h>
#include <nvs.h>
#include <nvs_flash.h>
#include <driver/gpio.h>
#include <esp_event.h>
#include <ssid_manager.h>
#include <wifi_station.h>
#include <esp_netif.h>
#include <esp_system.h>  // 添加获取MAC地址所需的头文件
#include <esp_mac.h>    // ESP-IDF v5.x 中的MAC地址相关API

#include "application.h"
#include "system_info.h"
#include "settings.h"
#include "ble_provisioning.h" // 添加蓝牙配网头文件


//extern spi_device_handle_t dev_handle;  // 声明外部变量

static const char* MAIN_TAG = "main";

// 打印WiFi配置信息的函数
void print_wifi_config() {
    ESP_LOGI(MAIN_TAG, "=== WiFi配置信息 ===");
    
    // 从NVS中读取WiFi配置
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("wifi", NVS_READONLY, &nvs_handle);
    bool force_ap = false;
    
    if (err == ESP_OK) {
        uint8_t value = 0;
        err = nvs_get_u8(nvs_handle, "force_ap", &value);
        if (err == ESP_OK) {
            force_ap = (value == 1);
        }
        nvs_close(nvs_handle);
    }
    
    ESP_LOGI(MAIN_TAG, "force_ap: %d", force_ap);
    
    // 获取保存的SSID列表
    auto& ssid_manager = SsidManager::GetInstance();
    auto ssid_list = ssid_manager.GetSsidList();
    ESP_LOGI(MAIN_TAG, "已保存的WiFi网络数量: %d", ssid_list.size());
    
    for (size_t i = 0; i < ssid_list.size(); i++) {
        ESP_LOGI(MAIN_TAG, "SSID %d: %s", i + 1, ssid_list[i].ssid.c_str());
        // 打印密码
        ESP_LOGI(MAIN_TAG, "  密码: %s", ssid_list[i].password.c_str());
    }
}

// 尝试蓝牙配网的函数
bool try_ble_provisioning()
{


    // 检查是否强制进入AP模式
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("wifi", NVS_READONLY, &nvs_handle);
    bool force_ap = false;
    
    if (err == ESP_OK) {
        uint8_t value = 0;
        err = nvs_get_u8(nvs_handle, "force_ap", &value);
        if (err == ESP_OK) {
            force_ap = (value == 1);
        }
        nvs_close(nvs_handle);
    }
    
    // 如果强制进入AP模式，跳过蓝牙配网
    if (force_ap) {
        ESP_LOGI(MAIN_TAG, "强制进入AP模式，跳过蓝牙配网");
        return false;
    }
    
    // 检查是否已有保存的WiFi网络
    auto& ssid_manager = SsidManager::GetInstance();
    auto ssid_list = ssid_manager.GetSsidList();
    ESP_LOGI(MAIN_TAG, "已保存的WiFi网络数量: %d", ssid_list.size());
    
    // 如果已有保存的WiFi网络，跳过蓝牙配网
    if (!ssid_list.empty()) {
        ESP_LOGI(MAIN_TAG, "已有保存的WiFi网络，跳过蓝牙配网");
        return false;
    }
    
    // 确保esp_netif已初始化
    esp_err_t ret = esp_netif_init();
    if (ret != ESP_OK) {
        ESP_LOGE(MAIN_TAG, "网络接口初始化失败: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 获取MAC地址并创建唯一设备名称
    uint8_t mac[6];
    esp_efuse_mac_get_default(mac);
    char device_name[32];
    sprintf(device_name, "PROV_%02X%02X%02X", mac[3], mac[4], mac[5]);
    const char* pop = "123456"; // 安全密钥
    
    ESP_LOGI(MAIN_TAG, "开始蓝牙配网过程...");
    ESP_LOGI(MAIN_TAG, "设备名称: %s", device_name);

    
    // 启动蓝牙配网并等待完成
    bool result = ble_prov_start(device_name, pop, true);
    
    if (result) {
        ESP_LOGI(MAIN_TAG, "蓝牙配网成功，将重启设备以连接WiFi");
        // 延迟一段时间后重启
        vTaskDelay(3000 / portTICK_PERIOD_MS);
        esp_restart();
        return true;
    } else {
        ESP_LOGW(MAIN_TAG, "蓝牙配网失败或超时，将继续启动应用程序");
        return false;
    }
}

extern "C" void app_main(void)
{
    // Initialize the default event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // Initialize NVS flash for WiFi configuration
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_LOGW(MAIN_TAG, "Erasing NVS flash to fix corruption");
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    
    
    // 打印WiFi配置信息
    print_wifi_config();
    
    // // 尝试蓝牙配网
    // if (try_ble_provisioning()) {
    //     // 如果蓝牙配网成功并重启，下面的代码不会执行
    //     return;
    // }

    // Launch the application
    Application::GetInstance().Start();
    // The main thread will exit and release the stack memory
}
